<?php
/**
 * Edit Interview Form
 * Form for editing an existing interview
 */

$settings = json_decode($interview['interview_settings'], true) ?? [];
?>

<?= $this->extend('templates/nolstemp') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h4 class="mb-1">Edit Interview</h4>
                    <p class="text-muted mb-0"><?= esc($interview['interview_title']) ?></p>
                </div>
                <div>
                    <a href="<?= base_url('interviews/dashboard/' . $interview['id']) ?>" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Error Messages -->
    <?php if (session()->getFlashdata('errors')): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <h6>Please fix the following errors:</h6>
            <ul class="mb-0">
                <?php foreach (session()->getFlashdata('errors') as $error): ?>
                    <li><?= esc($error) ?></li>
                <?php endforeach; ?>
            </ul>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- Edit Interview Form -->
    <div class="card shadow-sm">
        <div class="card-header bg-warning text-dark">
            <h5 class="mb-0">
                <i class="fas fa-edit me-2"></i>Interview Information
            </h5>
        </div>
        <div class="card-body">
            <form action="<?= base_url('interviews/update/' . $interview['id']) ?>" method="POST">
                <?= csrf_field() ?>
                
                <div class="row g-3">
                    <!-- Interview Title -->
                    <div class="col-md-8">
                        <label for="interview_title" class="form-label">Interview Title <span class="text-danger">*</span></label>
                        <input type="text"
                               class="form-control"
                               id="interview_title"
                               name="interview_title"
                               value="<?= old('interview_title', $interview['interview_title']) ?>"
                               placeholder="Enter interview title"
                               required>
                    </div>

                    <!-- Interview Settings (JSON) -->
                    <div class="col-12">
                        <label for="interview_settings" class="form-label">Interview Settings</label>
                        <input type="hidden" name="interview_settings" value='<?= esc($interview['interview_settings']) ?>' id="interview_settings">
                        <div class="form-text">
                            Interview settings are configured separately through the Interview Settings feature.
                            <br>Current settings: <code><?= esc($interview['interview_settings']) ?></code>
                        </div>
                    </div>

                    <!-- Remarks -->
                    <div class="col-12">
                        <label for="remarks" class="form-label">Remarks</label>
                        <textarea class="form-control" 
                                  id="remarks" 
                                  name="remarks" 
                                  rows="3"
                                  placeholder="Enter any additional remarks or notes"><?= old('remarks', $interview['remarks']) ?></textarea>
                    </div>
                </div>

                <!-- Form Actions -->
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="d-flex justify-content-end gap-2">
                            <a href="<?= base_url('interviews/dashboard/' . $interview['id']) ?>" class="btn btn-secondary">
                                <i class="fas fa-times me-2"></i>Cancel
                            </a>
                            <button type="submit" class="btn btn-warning">
                                <i class="fas fa-save me-2"></i>Update Interview
                            </button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>


<?= $this->endSection() ?>
