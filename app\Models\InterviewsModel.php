<?php

namespace App\Models;

use CodeIgniter\Model;

/**
 * InterviewsModel
 * 
 * Model for managing interviews table
 * 
 * Table Structure:
 * CREATE TABLE interviews (
 *     id INT AUTO_INCREMENT PRIMARY KEY,
 *     org_id INT NOT NULL,
 *     exercise_id INT NOT NULL,
 *     interview_title VARCHAR(255) NOT NULL,
 *     interview_settings JSON NOT NULL,
 *     status VARCHAR(100) NOT NULL,
 *     remarks TEXT,
 *     created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
 *     updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
 * );
 * 
 * Indexes:
 * CREATE INDEX idx_interviews_org_id ON interviews(org_id);
 * CREATE INDEX idx_interviews_exercise_id ON interviews(exercise_id);
 */
class InterviewsModel extends Model
{
    protected $table         = 'interviews';
    protected $primaryKey    = 'id';
    protected $useAutoIncrement = true;
    protected $returnType    = 'array';
    protected $useSoftDeletes = false;
    protected $protectFields = true;

    // Fields that are allowed to be set during insert/update operations.
    protected $allowedFields = [
        'org_id',
        'exercise_id',
        'interview_title',
        'interview_settings',
        'status',
        'remarks'
    ];

    // Enable automatic handling of created_at and updated_at fields.
    protected $useTimestamps = true;
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $dateFormat    = 'datetime';

    // Validation rules
    protected $validationRules = [
        'org_id'             => 'required|numeric',
        'exercise_id'        => 'required|numeric',
        'interview_title'    => 'required|max_length[255]',
        'interview_settings' => 'required',
        'status'            => 'required|max_length[100]',
        'remarks'           => 'permit_empty'
    ];

    protected $validationMessages = [
        'org_id' => [
            'required' => 'Organization ID is required',
            'numeric'  => 'Organization ID must be a number'
        ],
        'exercise_id' => [
            'required' => 'Exercise ID is required',
            'numeric'  => 'Exercise ID must be a number'
        ],
        'interview_title' => [
            'required'   => 'Interview title is required',
            'max_length' => 'Interview title cannot exceed 255 characters'
        ],
        'interview_settings' => [
            'required' => 'Interview settings are required'
        ],
        'status' => [
            'required'   => 'Status is required',
            'max_length' => 'Status cannot exceed 100 characters'
        ]
    ];

    protected $skipValidation = false;
    protected $cleanValidationRules = true;

    /**
     * Get interviews by organization ID
     *
     * @param int $orgId
     * @return array
     */
    public function getInterviewsByOrgId($orgId)
    {
        return $this->where('org_id', $orgId)
                    ->orderBy('created_at', 'DESC')
                    ->findAll();
    }

    /**
     * Get interviews by exercise ID
     *
     * @param int $exerciseId
     * @return array
     */
    public function getInterviewsByExerciseId($exerciseId)
    {
        return $this->where('exercise_id', $exerciseId)
                    ->orderBy('created_at', 'DESC')
                    ->findAll();
    }

    /**
     * Get interviews with related data (organization and exercise details)
     *
     * @param int $exerciseId Optional exercise ID to filter by
     * @return array
     */
    public function getInterviewsWithDetails($exerciseId = null)
    {
        $builder = $this->select('
            interviews.*,
            dakoii_org.org_name,
            dakoii_org.org_code,
            exercises.exercise_name,
            exercises.advertisement_no,
            exercises.status as exercise_status
        ')
        ->join('dakoii_org', 'interviews.org_id = dakoii_org.id', 'left')
        ->join('exercises', 'interviews.exercise_id = exercises.id', 'left');

        if ($exerciseId !== null) {
            $builder->where('interviews.exercise_id', $exerciseId);
        }

        return $builder->orderBy('interviews.created_at', 'DESC')
                      ->findAll();
    }

    /**
     * Get interview with organization and exercise details
     *
     * @param int $interviewId
     * @return array|null
     */
    public function getInterviewWithDetails($interviewId)
    {
        return $this->select('
            interviews.*,
            dakoii_org.org_name,
            dakoii_org.org_code,
            exercises.exercise_name,
            exercises.advertisement_no,
            exercises.status as exercise_status
        ')
        ->join('dakoii_org', 'interviews.org_id = dakoii_org.id', 'left')
        ->join('exercises', 'interviews.exercise_id = exercises.id', 'left')
        ->where('interviews.id', $interviewId)
        ->first();
    }

    /**
     * Get interviews count by exercise ID
     *
     * @param int $exerciseId
     * @return int
     */
    public function getInterviewsCount($exerciseId)
    {
        return $this->where('exercise_id', $exerciseId)->countAllResults();
    }

    /**
     * Get recent interviews (last 30 days)
     *
     * @param int $orgId Optional organization ID to filter by
     * @param int $limit Number of interviews to return
     * @return array
     */
    public function getRecentInterviews($orgId = null, $limit = 10)
    {
        $builder = $this->select('
            interviews.*,
            dakoii_org.org_name,
            exercises.exercise_name
        ')
        ->join('dakoii_org', 'interviews.org_id = dakoii_org.id', 'left')
        ->join('exercises', 'interviews.exercise_id = exercises.id', 'left')
        ->where('interviews.created_at >=', date('Y-m-d H:i:s', strtotime('-30 days')));

        if ($orgId !== null) {
            $builder->where('interviews.org_id', $orgId);
        }

        return $builder->orderBy('interviews.created_at', 'DESC')
                      ->limit($limit)
                      ->findAll();
    }

    /**
     * Search interviews by title
     *
     * @param string $searchTerm
     * @param int $orgId Optional organization ID to filter by
     * @return array
     */
    public function searchInterviewsByTitle($searchTerm, $orgId = null)
    {
        $builder = $this->select('
            interviews.*,
            dakoii_org.org_name,
            exercises.exercise_name
        ')
        ->join('dakoii_org', 'interviews.org_id = dakoii_org.id', 'left')
        ->join('exercises', 'interviews.exercise_id = exercises.id', 'left')
        ->like('interviews.interview_title', $searchTerm);

        if ($orgId !== null) {
            $builder->where('interviews.org_id', $orgId);
        }

        return $builder->orderBy('interviews.created_at', 'DESC')
                      ->findAll();
    }

    /**
     * Get interviews with statistics for reports
     *
     * @param int $orgId Optional organization ID to filter by
     * @return array
     */
    public function getInterviewsForReports($orgId = null)
    {
        $builder = $this->select('
            interviews.*,
            dakoii_org.org_name,
            dakoii_org.org_code,
            exercises.exercise_name,
            exercises.advertisement_no,
            exercises.status as exercise_status
        ')
        ->join('dakoii_org', 'interviews.org_id = dakoii_org.id', 'left')
        ->join('exercises', 'interviews.exercise_id = exercises.id', 'left');

        if ($orgId !== null) {
            $builder->where('interviews.org_id', $orgId);
        }

        $interviews = $builder->orderBy('interviews.created_at', 'DESC')
                            ->findAll();

        // Add statistics to each interview
        foreach ($interviews as &$interview) {
            // Get interview positions count
            $positionsCount = $this->db->table('interview_positions')
                ->where('exercise_id', $interview['exercise_id'])
                ->countAllResults();

            $interview['positions_count'] = $positionsCount;
        }

        return $interviews;
    }

    /**
     * Check if an interview exists for a specific exercise
     *
     * @param int $exerciseId
     * @return bool
     */
    public function hasInterviewForExercise($exerciseId)
    {
        $result = $this->where('exercise_id', $exerciseId)->first();
        return $result !== null;
    }

    /**
     * Get interview settings as array (decode JSON)
     *
     * @param int $interviewId
     * @return array|null
     */
    public function getInterviewSettings($interviewId)
    {
        $interview = $this->find($interviewId);
        if ($interview && isset($interview['interview_settings'])) {
            return json_decode($interview['interview_settings'], true);
        }
        return null;
    }

    /**
     * Update interview settings (encode as JSON)
     *
     * @param int $interviewId
     * @param array $settings
     * @return bool
     */
    public function updateInterviewSettings($interviewId, $settings)
    {
        return $this->update($interviewId, [
            'interview_settings' => json_encode($settings)
        ]);
    }
}
