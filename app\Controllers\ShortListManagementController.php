<?php

namespace App\Controllers;

use CodeIgniter\RESTful\ResourceController;

class ShortListManagementController extends ResourceController
{
    protected $helpers = ['form', 'url', 'info'];
    protected $session;
    protected $exerciseModel;
    protected $positionsModel;
    protected $applicationModel;
    protected $ratingModel;

    public function __construct()
    {
        $this->session = session();
        helper(['form', 'url', 'info']);

        // Load models
        $this->exerciseModel = new \App\Models\ExerciseModel();
        $this->positionsModel = new \App\Models\PositionsModel();
        $this->applicationModel = new \App\Models\AppxApplicationDetailsModel();
        $this->ratingModel = new \App\Models\AppxApplicationRatingModel();
    }

    /**
     * List exercises for shortlisting (Dashboard -> Exercises)
     */
    public function index()
    {
        // Get organization ID from session
        $orgId = session()->get('org_id');
        if (!$orgId) {
            session()->setFlashdata('error', 'Organization context not found.');
            return redirect()->to(base_url());
        }

        // Get exercises with status = 'selection' for this organization
        $exercises = $this->exerciseModel->where('status', 'selection')
                                        ->where('org_id', $orgId)
                                        ->orderBy('created_at', 'DESC')
                                        ->findAll();

        $data = [
            'title' => 'Shortlisting - Select Exercise',
            'menu' => 'shortlisting',
            'exercises' => $exercises
        ];

        return view('application_shortlisting/application_shortlisting_exercises', $data);
    }

    /**
     * List position groups for an exercise (Exercises -> Position Groups)
     */
    public function positionGroups($exerciseId)
    {
        // Load PositionsGroupModel for position groups
        $positionGroupModel = new \App\Models\PositionsGroupModel();

        // Get exercise details
        $exercise = $this->positionsModel->select('
                exercises.id,
                exercises.exercise_name,
                exercises.advertisement_no,
                dakoii_org.org_name
            ')
            ->join('positions_groups', 'positions.position_group_id = positions_groups.id', 'left')
            ->join('exercises', 'positions_groups.exercise_id = exercises.id', 'left')
            ->join('dakoii_org', 'positions.org_id = dakoii_org.id', 'left')
            ->where('exercises.id', $exerciseId)
            ->first();

        if (!$exercise) {
            return redirect()->to('/shortlisting')->with('error', 'Exercise not found');
        }

        // Get position groups for this exercise with position count
        $positionGroups = $positionGroupModel->getPositionGroupsWithCountByExerciseId($exerciseId);

        $data = [
            'title' => 'Shortlisting - Select Position Group',
            'menu' => 'shortlisting',
            'exercise' => $exercise,
            'positionGroups' => $positionGroups
        ];

        return view('application_shortlisting/application_shortlisting_position_groups', $data);
    }

    /**
     * List positions for a position group (Position Groups -> Positions)
     */
    public function positions($positionGroupId)
    {
        // Load PositionsGroupModel for position group details
        $positionGroupModel = new \App\Models\PositionsGroupModel();

        // Get position group details
        $positionGroup = $positionGroupModel->select('
                positions_groups.id,
                positions_groups.group_name,
                positions_groups.exercise_id,
                exercises.exercise_name,
                exercises.advertisement_no,
                dakoii_org.org_name
            ')
            ->join('exercises', 'positions_groups.exercise_id = exercises.id', 'left')
            ->join('dakoii_org', 'positions_groups.org_id = dakoii_org.id', 'left')
            ->where('positions_groups.id', $positionGroupId)
            ->first();

        if (!$positionGroup) {
            return redirect()->to('/shortlisting')->with('error', 'Position group not found');
        }

        // Get positions in this group with application counts
        $positions = $this->positionsModel->select('
                positions.id,
                positions.designation,
                positions.classification,
                positions.location,
                COUNT(appx_application_details.id) as application_count,
                SUM(CASE WHEN appx_application_details.rating_status = "completed" THEN 1 ELSE 0 END) as rated_count
            ')
            ->join('appx_application_details', 'positions.id = appx_application_details.position_id', 'left')
            ->where('positions.position_group_id', $positionGroupId)
            ->groupBy('positions.id')
            ->orderBy('positions.designation', 'ASC')
            ->findAll();

        $data = [
            'title' => 'Shortlisting - Select Position',
            'menu' => 'shortlisting',
            'positionGroup' => $positionGroup,
            'positions' => $positions
        ];

        return view('application_shortlisting/application_shortlisting_positions', $data);
    }

    /**
     * List applications for a position (Positions -> Applications) - Sorted by highest score to lowest
     */
    public function applications($positionId)
    {
        // Get position details with related information
        $position = $this->positionsModel->select('
                positions.*,
                positions_groups.id as position_group_id,
                positions_groups.group_name,
                positions_groups.exercise_id,
                exercises.exercise_name,
                exercises.advertisement_no,
                dakoii_org.org_name
            ')
            ->join('positions_groups', 'positions.position_group_id = positions_groups.id', 'left')
            ->join('exercises', 'positions_groups.exercise_id = exercises.id', 'left')
            ->join('dakoii_org', 'positions.org_id = dakoii_org.id', 'left')
            ->where('positions.id', $positionId)
            ->first();

        if (!$position) {
            return redirect()->to('/shortlisting')->with('error', 'Position not found');
        }

        // Get applications for this position that are ready for shortlisting (rated applications)
        // Sort by highest score to lowest score
        $applications = $this->applicationModel->select('
                appx_application_details.*,
                positions.designation as position_title
            ')
            ->join('positions', 'appx_application_details.position_id = positions.id', 'left')
            ->where('appx_application_details.position_id', $positionId)
            ->where('appx_application_details.rating_status', 'completed')
            ->orderBy('appx_application_details.rating_capability_max', 'DESC')
            ->findAll();

        $data = [
            'title' => 'Shortlisting - Applications',
            'menu' => 'shortlisting',
            'position' => $position,
            'applications' => $applications
        ];

        return view('application_shortlisting/application_shortlisting_applications', $data);
    }

    /**
     * Display shortlisting detail form for a specific application
     */
    public function detail($applicationId)
    {
        // Get application details
        $application = $this->applicationModel->find($applicationId);
        if (!$application) {
            return redirect()->to('/shortlisting')->with('error', 'Application not found');
        }

        // Get position details with related information
        $position = $this->positionsModel->select('
                positions.*,
                positions_groups.id as position_group_id,
                positions_groups.group_name,
                positions_groups.exercise_id,
                exercises.exercise_name,
                exercises.advertisement_no,
                dakoii_org.org_name
            ')
            ->join('positions_groups', 'positions.position_group_id = positions_groups.id', 'left')
            ->join('exercises', 'positions_groups.exercise_id = exercises.id', 'left')
            ->join('dakoii_org', 'positions.org_id = dakoii_org.id', 'left')
            ->where('positions.id', $application['position_id'])
            ->first();

        if (!$position) {
            return redirect()->to('/shortlisting')->with('error', 'Position not found');
        }

        $data = [
            'title' => 'Shortlist Application',
            'menu' => 'shortlisting',
            'position' => $position,
            'application' => $application
        ];

        return view('application_shortlisting/application_shortlisting_detail', $data);
    }

    /**
     * Update shortlisting status for an application
     */
    public function updateStatus($applicationId = null)
    {
        // Get application details
        $application = $this->applicationModel->find($applicationId);
        if (!$application) {
            return redirect()->to('/shortlisting')->with('error', 'Application not found');
        }

        // Validate input
        $rules = [
            'shortlist_status' => 'required|in_list[shortlisted,eliminated,withdrawn]',
            'shortlist_remarks' => 'permit_empty|string|max_length[1000]'
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()
                ->withInput()
                ->with('error', 'Please correct the validation errors.');
        }

        // Update application shortlisting data
        $updateData = [
            'shortlist_status' => $this->request->getPost('shortlist_status'),
            'shortlisted_by' => $this->session->get('user_id'),
            'shortlisted_at' => date('Y-m-d H:i:s'),
            'shortlist_remarks' => trim($this->request->getPost('shortlist_remarks')),
            'updated_by' => $this->session->get('user_id')
        ];

        if ($this->applicationModel->update($applicationId, $updateData)) {
            return redirect()->to('/shortlisting/applications/' . $application['position_id'])
                ->with('success', 'Application shortlisting status updated successfully.');
        } else {
            return redirect()->back()
                ->with('error', 'Failed to update shortlisting status')
                ->withInput();
        }
    }
}
