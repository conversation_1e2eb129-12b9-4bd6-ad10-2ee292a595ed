<?= $this->extend('templates/nolstemp') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Page Header -->
    <div class="card mb-4 border-start border-primary border-4">
        <div class="card-body">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="h4 fw-bold text-gray-800 mb-1">Shortlisting - Select Position</h2>
                    <p class="text-muted mb-0">Select a position to view applications for shortlisting</p>
                </div>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb mb-0">
                        <li class="breadcrumb-item"><a href="<?= base_url('shortlisting') ?>" class="text-decoration-none">Exercises</a></li>
                        <li class="breadcrumb-item">
                            <a href="<?= base_url('shortlisting/position-groups/' . $positionGroup['exercise_id']) ?>" class="text-decoration-none">
                                Position Groups
                            </a>
                        </li>
                        <li class="breadcrumb-item active" aria-current="page"><?= esc($positionGroup['group_name']) ?></li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>

    <!-- Position Group Information -->
    <div class="card mb-4">
        <div class="card-body">
            <div class="row">
                <div class="col-md-8">
                    <h5 class="card-title mb-3">
                        <i class="fas fa-users me-2 text-primary"></i>
                        <?= esc($positionGroup['group_name']) ?>
                    </h5>
                    <div class="row">
                        <div class="col-sm-6">
                            <p class="mb-1"><strong>Exercise:</strong> <?= esc($positionGroup['exercise_name']) ?></p>
                        </div>
                        <div class="col-sm-6">
                            <p class="mb-1"><strong>Advertisement No:</strong> <?= esc($positionGroup['advertisement_no']) ?></p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 text-end">
                    <a href="<?= base_url('shortlisting/position-groups/' . $positionGroup['exercise_id']) ?>" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-1"></i> Back to Position Groups
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Flash Messages -->
    <?php if (session()->getFlashdata('success')): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>
            <?= session()->getFlashdata('success') ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if (session()->getFlashdata('error')): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i>
            <?= session()->getFlashdata('error') ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- Positions List -->
    <div class="card shadow-sm">
        <div class="card-header bg-white py-3">
            <h5 class="card-title mb-0">Positions</h5>
        </div>
        <div class="card-body p-0">
            <?php if (empty($positions)): ?>
                <div class="text-center py-5">
                    <i class="fas fa-briefcase fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No positions found</h5>
                    <p class="text-muted">No positions are available for shortlisting in this position group.</p>
                </div>
            <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-hover align-middle mb-0">
                        <thead class="table-light">
                            <tr>
                                <th class="px-4">Position</th>
                                <th>Classification</th>
                                <th>Location</th>
                                <th>Applications</th>
                                <th>Rated</th>
                                <th>Action</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($positions as $position): ?>
                                <tr>
                                    <td class="px-4">
                                        <strong><?= esc($position['designation']) ?></strong>
                                    </td>
                                    <td>
                                        <span class="badge bg-secondary">
                                            <?= esc($position['classification']) ?>
                                        </span>
                                    </td>
                                    <td>
                                        <i class="fas fa-map-marker-alt me-1 text-muted"></i>
                                        <?= esc($position['location']) ?>
                                    </td>
                                    <td>
                                        <span class="badge bg-info">
                                            <?= $position['application_count'] ?> Application<?= $position['application_count'] != 1 ? 's' : '' ?>
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge bg-success">
                                            <?= $position['rated_count'] ?> Rated
                                        </span>
                                    </td>
                                    <td>
                                        <?php if ($position['rated_count'] > 0): ?>
                                            <a href="<?= base_url('shortlisting/applications/' . $position['id']) ?>" 
                                               class="btn btn-primary btn-sm">
                                                <i class="fas fa-list"></i> View Applications
                                            </a>
                                        <?php else: ?>
                                            <span class="text-muted small">No rated applications</span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>
<?= $this->endSection() ?>
