<?php
/**
 * Interview Dashboard - Main dashboard for managing interview
 * Shows interview overview with buttons for manage positions and interview settings
 */
?>

<?= $this->extend('templates/nolstemp') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h4 class="mb-1">Interview Dashboard</h4>
                    <p class="text-muted mb-0"><?= esc($interview['interview_title']) ?></p>
                </div>
                <div>
                    <a href="<?= base_url('interviews/interviews/' . $interview['exercise_id']) ?>" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Back to Interviews
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Success/Error Messages -->
    <?php if (session()->getFlashdata('success')): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <?= session()->getFlashdata('success') ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if (session()->getFlashdata('error')): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <?= session()->getFlashdata('error') ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- Interview Overview -->
    <div class="row mb-4">
        <div class="col-md-8">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>Interview Information
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-md-6">
                            <label class="form-label fw-bold">Interview Title</label>
                            <p class="form-control-plaintext"><?= esc($interview['interview_title']) ?></p>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label fw-bold">Exercise</label>
                            <p class="form-control-plaintext"><?= esc($interview['exercise_name']) ?></p>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label fw-bold">Organization</label>
                            <p class="form-control-plaintext"><?= esc($interview['org_name']) ?></p>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label fw-bold">Created</label>
                            <p class="form-control-plaintext">
                                <?= date('M d, Y h:i A', strtotime($interview['created_at'])) ?>
                            </p>
                        </div>
                        <div class="col-12">
                            <label class="form-label fw-bold">Remarks</label>
                            <p class="form-control-plaintext">
                                <?= esc($interview['remarks'] ?: 'No remarks provided') ?>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card shadow-sm">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-bar me-2"></i>Statistics
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row g-3 text-center">
                        <div class="col-12">
                            <div class="border rounded p-3">
                                <h3 class="text-primary mb-1"><?= $positions_count ?></h3>
                                <small class="text-muted">Positions</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Action Buttons -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header bg-white">
                    <h5 class="mb-0">
                        <i class="fas fa-cogs me-2"></i>Interview Management
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-md-6">
                            <div class="d-grid">
                                <a href="<?= base_url('interviews/manage-positions/' . $interview['id']) ?>" 
                                   class="btn btn-primary btn-lg">
                                    <i class="fas fa-users me-2"></i>
                                    <div>
                                        <div class="fw-bold">Manage Positions</div>
                                        <small>Add and manage interview positions</small>
                                    </div>
                                </a>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="d-grid">
                                <a href="<?= base_url('interviews/settings/' . $interview['id']) ?>" 
                                   class="btn btn-success btn-lg">
                                    <i class="fas fa-cog me-2"></i>
                                    <div>
                                        <div class="fw-bold">Interview Settings</div>
                                        <small>Configure interview parameters</small>
                                    </div>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header bg-white">
                    <h5 class="mb-0">
                        <i class="fas fa-bolt me-2"></i>Quick Actions
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row g-2">
                        <div class="col-auto">
                            <a href="<?= base_url('interviews/edit/' . $interview['id']) ?>" 
                               class="btn btn-outline-primary">
                                <i class="fas fa-edit me-2"></i>Edit Interview
                            </a>
                        </div>
                        <div class="col-auto">
                            <button type="button" class="btn btn-outline-info" 
                                    data-bs-toggle="modal" 
                                    data-bs-target="#interviewDetailsModal">
                                <i class="fas fa-eye me-2"></i>View Details
                            </button>
                        </div>
                        <div class="col-auto">
                            <button type="button" class="btn btn-outline-danger" 
                                    onclick="confirmDelete(<?= $interview['id'] ?>, '<?= esc($interview['interview_title']) ?>')">
                                <i class="fas fa-trash me-2"></i>Delete Interview
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Interview Details Modal -->
<div class="modal fade" id="interviewDetailsModal" tabindex="-1" aria-labelledby="interviewDetailsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="interviewDetailsModalLabel">
                    <i class="fas fa-info-circle me-2"></i>Interview Details
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="row g-3">
                    <div class="col-md-6">
                        <label class="form-label fw-bold">Interview ID</label>
                        <p class="form-control-plaintext"><?= $interview['id'] ?></p>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label fw-bold">Exercise ID</label>
                        <p class="form-control-plaintext"><?= $interview['exercise_id'] ?></p>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label fw-bold">Organization ID</label>
                        <p class="form-control-plaintext"><?= $interview['org_id'] ?></p>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label fw-bold">Last Updated</label>
                        <p class="form-control-plaintext">
                            <?= date('M d, Y h:i A', strtotime($interview['updated_at'])) ?>
                        </p>
                    </div>
                    <div class="col-12">
                        <label class="form-label fw-bold">Interview Settings</label>
                        <pre class="bg-light p-3 rounded"><?= json_encode(json_decode($interview['interview_settings'], true), JSON_PRETTY_PRINT) ?></pre>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title" id="deleteModalLabel">
                    <i class="fas fa-exclamation-triangle me-2"></i>Confirm Delete
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete this interview?</p>
                <p class="text-muted mb-0">This action cannot be undone.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-trash me-2"></i>Delete Interview
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function confirmDelete(interviewId, interviewTitle) {
    document.getElementById('deleteForm').action = `<?= base_url('interviews/delete/') ?>${interviewId}`;
    
    const deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
    deleteModal.show();
}
</script>
<?= $this->endSection() ?>
