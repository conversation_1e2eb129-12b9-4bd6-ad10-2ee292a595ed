<?= $this->extend('templates/nolstemp') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Page Header -->
    <div class="card mb-4 border-start border-primary border-4">
        <div class="card-body">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="h4 fw-bold text-gray-800 mb-1">Shortlist Application</h2>
                    <p class="text-muted mb-0">Review and update shortlisting status</p>
                </div>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb mb-0">
                        <li class="breadcrumb-item"><a href="<?= base_url('shortlisting') ?>" class="text-decoration-none">Exercises</a></li>
                        <li class="breadcrumb-item">
                            <a href="<?= base_url('shortlisting/position-groups/' . $position['exercise_id']) ?>" class="text-decoration-none">
                                Position Groups
                            </a>
                        </li>
                        <li class="breadcrumb-item">
                            <a href="<?= base_url('shortlisting/positions/' . $position['position_group_id']) ?>" class="text-decoration-none">
                                Positions
                            </a>
                        </li>
                        <li class="breadcrumb-item">
                            <a href="<?= base_url('shortlisting/applications/' . $position['id']) ?>" class="text-decoration-none">
                                Applications
                            </a>
                        </li>
                        <li class="breadcrumb-item active" aria-current="page">Shortlist</li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>

    <!-- Flash Messages -->
    <?php if (session()->getFlashdata('success')): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>
            <?= session()->getFlashdata('success') ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if (session()->getFlashdata('error')): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i>
            <?= session()->getFlashdata('error') ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <div class="row">
        <!-- Application Information -->
        <div class="col-md-8">
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-user me-2"></i>
                        Application Information
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="text-muted">Personal Information</h6>
                            <p><strong>Name:</strong> <?= esc($application['first_name']) ?> <?= esc($application['last_name']) ?></p>
                            <p><strong>Application No:</strong> <?= esc($application['application_number']) ?></p>
                            <p><strong>Gender:</strong> <?= esc($application['gender']) ?></p>
                            <p><strong>Date of Birth:</strong> <?= date('M d, Y', strtotime($application['date_of_birth'])) ?></p>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-muted">Position Information</h6>
                            <p><strong>Position:</strong> <?= esc($position['designation']) ?></p>
                            <p><strong>Classification:</strong> <?= esc($position['classification']) ?></p>
                            <p><strong>Location:</strong> <?= esc($position['location']) ?></p>
                            <p><strong>Exercise:</strong> <?= esc($position['exercise_name']) ?></p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Rating Information -->
            <div class="card mb-4">
                <div class="card-header bg-success text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-star me-2"></i>
                        Rating Information
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="text-center">
                                <h3 class="text-primary"><?= $application['rating_capability_max'] ?? 0 ?></h3>
                                <p class="text-muted">Total Score</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <p><strong>Rating Status:</strong> 
                                <span class="badge bg-success"><?= ucfirst($application['rating_status']) ?></span>
                            </p>
                            <?php if (!empty($application['rating_remarks'])): ?>
                                <p><strong>Rating Remarks:</strong></p>
                                <p class="text-muted"><?= esc($application['rating_remarks']) ?></p>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Shortlisting Form -->
        <div class="col-md-4">
            <div class="card">
                <div class="card-header bg-warning text-dark">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-clipboard-check me-2"></i>
                        Shortlisting Decision
                    </h5>
                </div>
                <div class="card-body">
                    <?= form_open('shortlisting/update/' . $application['id']) ?>
                        <div class="mb-3">
                            <label for="shortlist_status" class="form-label">Shortlist Status <span class="text-danger">*</span></label>
                            <select class="form-select" id="shortlist_status" name="shortlist_status" required>
                                <option value="">Select Status</option>
                                <option value="shortlisted" <?= (old('shortlist_status') == 'shortlisted' || $application['shortlist_status'] == 'shortlisted') ? 'selected' : '' ?>>
                                    Shortlisted
                                </option>
                                <option value="eliminated" <?= (old('shortlist_status') == 'eliminated' || $application['shortlist_status'] == 'eliminated') ? 'selected' : '' ?>>
                                    Eliminated
                                </option>
                                <option value="withdrawn" <?= (old('shortlist_status') == 'withdrawn' || $application['shortlist_status'] == 'withdrawn') ? 'selected' : '' ?>>
                                    Withdrawn
                                </option>
                            </select>
                            <?php if (isset($validation) && $validation->hasError('shortlist_status')): ?>
                                <div class="text-danger small"><?= $validation->getError('shortlist_status') ?></div>
                            <?php endif; ?>
                        </div>

                        <div class="mb-3">
                            <label for="shortlist_remarks" class="form-label">Remarks</label>
                            <textarea class="form-control" id="shortlist_remarks" name="shortlist_remarks" rows="4" 
                                      placeholder="Enter remarks or justification for the shortlisting decision..."><?= old('shortlist_remarks', $application['shortlist_remarks'] ?? '') ?></textarea>
                            <?php if (isset($validation) && $validation->hasError('shortlist_remarks')): ?>
                                <div class="text-danger small"><?= $validation->getError('shortlist_remarks') ?></div>
                            <?php endif; ?>
                        </div>

                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>Update Shortlist Status
                            </button>
                            <a href="<?= base_url('shortlisting/applications/' . $position['id']) ?>" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-2"></i>Back to Applications
                            </a>
                        </div>
                    <?= form_close() ?>
                </div>
            </div>

            <!-- Current Status -->
            <?php if (!empty($application['shortlist_status'])): ?>
                <div class="card mt-3">
                    <div class="card-header bg-info text-white">
                        <h6 class="card-title mb-0">Current Status</h6>
                    </div>
                    <div class="card-body">
                        <p><strong>Status:</strong> 
                            <?php
                            $status = $application['shortlist_status'];
                            $badgeClass = match($status) {
                                'shortlisted' => 'bg-success',
                                'eliminated' => 'bg-danger',
                                'withdrawn' => 'bg-warning',
                                default => 'bg-secondary'
                            };
                            ?>
                            <span class="badge <?= $badgeClass ?>"><?= ucfirst($status) ?></span>
                        </p>
                        <?php if (!empty($application['shortlisted_at'])): ?>
                            <p><strong>Updated:</strong> <?= date('M d, Y H:i', strtotime($application['shortlisted_at'])) ?></p>
                        <?php endif; ?>
                        <?php if (!empty($application['shortlist_remarks'])): ?>
                            <p><strong>Remarks:</strong></p>
                            <p class="text-muted small"><?= esc($application['shortlist_remarks']) ?></p>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>
<?= $this->endSection() ?>
