<?php
/**
 * Create Interview Form
 * Form for creating a new interview for an exercise
 */
?>

<?= $this->extend('templates/nolstemp') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h4 class="mb-1">Create Interview</h4>
                    <p class="text-muted mb-0">Exercise: <?= esc($exercise['exercise_name']) ?></p>
                </div>
                <div>
                    <a href="<?= base_url('interviews/interviews/' . $exercise['id']) ?>" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Back to Interviews
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Error Messages -->
    <?php if (session()->getFlashdata('errors')): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <h6>Please fix the following errors:</h6>
            <ul class="mb-0">
                <?php foreach (session()->getFlashdata('errors') as $error): ?>
                    <li><?= esc($error) ?></li>
                <?php endforeach; ?>
            </ul>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- Create Interview Form -->
    <div class="card shadow-sm">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0">
                <i class="fas fa-plus me-2"></i>Interview Information
            </h5>
        </div>
        <div class="card-body">
            <form action="<?= base_url('interviews/store') ?>" method="POST">
                <?= csrf_field() ?>
                
                <!-- Hidden Fields -->
                <input type="hidden" name="org_id" value="<?= $exercise['org_id'] ?>">
                <input type="hidden" name="exercise_id" value="<?= $exercise['id'] ?>">
                
                <div class="row g-3">
                    <!-- Interview Title -->
                    <div class="col-md-8">
                        <label for="interview_title" class="form-label">Interview Title <span class="text-danger">*</span></label>
                        <input type="text"
                               class="form-control"
                               id="interview_title"
                               name="interview_title"
                               value="<?= old('interview_title') ?>"
                               placeholder="Enter interview title"
                               required>
                        <div class="form-text">Provide a descriptive title for this interview session</div>
                    </div>

                    <!-- Interview Settings (JSON) -->
                    <div class="col-12">
                        <label for="interview_settings" class="form-label">Interview Settings <span class="text-danger">*</span></label>
                        <input type="hidden" name="interview_settings" value='{}' id="interview_settings">
                        <div class="form-text">
                            Interview settings will be configured separately after creation through the Interview Settings feature.
                        </div>
                    </div>

                    <!-- Remarks -->
                    <div class="col-12">
                        <label for="remarks" class="form-label">Remarks</label>
                        <textarea class="form-control" 
                                  id="remarks" 
                                  name="remarks" 
                                  rows="3"
                                  placeholder="Enter any additional remarks or notes"><?= old('remarks') ?></textarea>
                    </div>
                </div>

                <!-- Form Actions -->
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="d-flex justify-content-end gap-2">
                            <a href="<?= base_url('interviews/interviews/' . $exercise['id']) ?>" class="btn btn-secondary">
                                <i class="fas fa-times me-2"></i>Cancel
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>Create Interview
                            </button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-generate interview title based on exercise name
    const exerciseName = '<?= esc($exercise['exercise_name']) ?>';
    const titleField = document.getElementById('interview_title');

    if (!titleField.value) {
        titleField.value = exerciseName + ' - Interview';
    }
});
</script>
<?= $this->endSection() ?>
