<?php

namespace App\Controllers;

use App\Models\ExerciseModel;
use App\Models\InterviewsModel;
use App\Models\InterviewPositionsModel;

class InterviewManagementController extends BaseController
{
    protected $session;
    protected $exerciseModel;
    protected $interviewsModel;
    protected $interviewPositionsModel;

    public function __construct()
    {
        helper(['form', 'url']);
        $this->session = \Config\Services::session();
        $this->exerciseModel = new ExerciseModel();
        $this->interviewsModel = new InterviewsModel();
        $this->interviewPositionsModel = new InterviewPositionsModel();
    }

    /**
     * List exercises with selection status for interview management
     */
    public function index()
    {
        // Get exercises in selection phase from database
        $exercises = $this->exerciseModel->where('status', 'selection')
                                        ->orderBy('created_at', 'DESC')
                                        ->findAll();

        $data = [
            'title' => 'Interview Management - Exercises',
            'exercises' => $exercises
        ];

        return view('application_interviews/application_interviews_index', $data);
    }

    /**
     * List interviews for a specific exercise
     */
    public function interviews($exerciseId)
    {
        $exercise = $this->exerciseModel->find($exerciseId);
        if (!$exercise) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Exercise not found');
        }

        $interviews = $this->interviewsModel->getInterviewsByExerciseId($exerciseId);

        $data = [
            'title' => 'Manage Interviews - ' . $exercise['exercise_name'],
            'exercise' => $exercise,
            'interviews' => $interviews
        ];

        return view('application_interviews/interviews_list', $data);
    }

    /**
     * Show create interview form
     */
    public function create($exerciseId)
    {
        $exercise = $this->exerciseModel->find($exerciseId);
        if (!$exercise) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Exercise not found');
        }

        $data = [
            'title' => 'Create Interview',
            'exercise' => $exercise
        ];

        return view('application_interviews/interviews_create', $data);
    }

    /**
     * Store new interview
     */
    public function store()
    {
        $validation = \Config\Services::validation();
        
        $validation->setRules([
            'org_id' => 'required|numeric',
            'exercise_id' => 'required|numeric',
            'interview_title' => 'required|max_length[255]',
            'interview_settings' => 'required',
            'status' => 'required|max_length[100]',
            'remarks' => 'permit_empty'
        ]);

        if (!$validation->withRequest($this->request)->run()) {
            return redirect()->back()->withInput()->with('errors', $validation->getErrors());
        }

        $data = [
            'org_id' => $this->request->getPost('org_id'),
            'exercise_id' => $this->request->getPost('exercise_id'),
            'interview_title' => $this->request->getPost('interview_title'),
            'interview_settings' => $this->request->getPost('interview_settings'),
            'remarks' => $this->request->getPost('remarks')
        ];

        if ($this->interviewsModel->insert($data)) {
            return redirect()->to(base_url('interviews/interviews/' . $data['exercise_id']))
                           ->with('success', 'Interview created successfully');
        } else {
            return redirect()->back()->withInput()->with('error', 'Failed to create interview');
        }
    }

    /**
     * Show edit interview form
     */
    public function edit($interviewId)
    {
        $interview = $this->interviewsModel->getInterviewWithDetails($interviewId);
        if (!$interview) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Interview not found');
        }

        $data = [
            'title' => 'Edit Interview',
            'interview' => $interview
        ];

        return view('application_interviews/interviews_edit', $data);
    }

    /**
     * Update interview
     */
    public function update($interviewId)
    {
        $interview = $this->interviewsModel->find($interviewId);
        if (!$interview) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Interview not found');
        }

        $validation = \Config\Services::validation();
        
        $validation->setRules([
            'interview_title' => 'required|max_length[255]',
            'interview_settings' => 'required',
            'remarks' => 'permit_empty'
        ]);

        if (!$validation->withRequest($this->request)->run()) {
            return redirect()->back()->withInput()->with('errors', $validation->getErrors());
        }

        $data = [
            'interview_title' => $this->request->getPost('interview_title'),
            'interview_settings' => $this->request->getPost('interview_settings'),
            'remarks' => $this->request->getPost('remarks')
        ];

        if ($this->interviewsModel->update($interviewId, $data)) {
            return redirect()->to(base_url('interviews/interviews/' . $interview['exercise_id']))
                           ->with('success', 'Interview updated successfully');
        } else {
            return redirect()->back()->withInput()->with('error', 'Failed to update interview');
        }
    }

    /**
     * Delete interview
     */
    public function delete($interviewId)
    {
        $interview = $this->interviewsModel->find($interviewId);
        if (!$interview) {
            return redirect()->back()->with('error', 'Interview not found');
        }

        if ($this->interviewsModel->delete($interviewId)) {
            return redirect()->to(base_url('interviews/interviews/' . $interview['exercise_id']))
                           ->with('success', 'Interview deleted successfully');
        } else {
            return redirect()->back()->with('error', 'Failed to delete interview');
        }
    }

    /**
     * Interview Dashboard - Main dashboard for managing interview
     */
    public function dashboard($interviewId)
    {
        $interview = $this->interviewsModel->getInterviewWithDetails($interviewId);
        if (!$interview) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Interview not found');
        }

        // Get interview positions count
        $positionsCount = $this->interviewPositionsModel->getInterviewPositionsCount($interview['exercise_id']);

        $data = [
            'title' => 'Interview Dashboard - ' . $interview['interview_title'],
            'interview' => $interview,
            'positions_count' => $positionsCount
        ];

        return view('application_interviews/interviews_dashboard', $data);
    }

    /**
     * Manage positions for interview
     */
    public function managePositions($interviewId)
    {
        $interview = $this->interviewsModel->getInterviewWithDetails($interviewId);
        if (!$interview) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Interview not found');
        }

        // Get interview positions with details
        $interviewPositions = $this->interviewPositionsModel->getInterviewPositionsWithDetails($interview['exercise_id']);

        $data = [
            'title' => 'Manage Positions - ' . $interview['interview_title'],
            'interview' => $interview,
            'interview_positions' => $interviewPositions
        ];

        return view('application_interviews/interviews_positions', $data);
    }

    /**
     * Show interview settings
     */
    public function settings($interviewId)
    {
        $interview = $this->interviewsModel->getInterviewWithDetails($interviewId);
        if (!$interview) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Interview not found');
        }

        // Decode interview settings
        $settings = json_decode($interview['interview_settings'], true) ?? [];

        $data = [
            'title' => 'Interview Settings - ' . $interview['interview_title'],
            'interview' => $interview,
            'settings' => $settings
        ];

        return view('application_interviews/interviews_settings', $data);
    }

    /**
     * Save interview settings
     */
    public function saveSettings($interviewId)
    {
        $interview = $this->interviewsModel->find($interviewId);
        if (!$interview) {
            return redirect()->back()->with('error', 'Interview not found');
        }

        $settings = $this->request->getPost('settings');
        
        if ($this->interviewsModel->updateInterviewSettings($interviewId, $settings)) {
            return redirect()->to(base_url('interviews/dashboard/' . $interviewId))
                           ->with('success', 'Interview settings saved successfully');
        } else {
            return redirect()->back()->with('error', 'Failed to save interview settings');
        }
    }
}
