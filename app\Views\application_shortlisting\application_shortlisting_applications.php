<?= $this->extend('templates/nolstemp') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Page Header -->
    <div class="card mb-4 border-start border-primary border-4">
        <div class="card-body">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="h4 fw-bold text-gray-800 mb-1">Shortlisting - Applications</h2>
                    <p class="text-muted mb-0">Applications sorted by highest score to lowest</p>
                </div>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb mb-0">
                        <li class="breadcrumb-item"><a href="<?= base_url('shortlisting') ?>" class="text-decoration-none">Exercises</a></li>
                        <li class="breadcrumb-item">
                            <a href="<?= base_url('shortlisting/position-groups/' . $position['exercise_id']) ?>" class="text-decoration-none">
                                Position Groups
                            </a>
                        </li>
                        <li class="breadcrumb-item">
                            <a href="<?= base_url('shortlisting/positions/' . $position['position_group_id']) ?>" class="text-decoration-none">
                                Positions
                            </a>
                        </li>
                        <li class="breadcrumb-item active" aria-current="page">Applications</li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>

    <!-- Position Information -->
    <div class="card mb-4">
        <div class="card-body">
            <div class="row">
                <div class="col-md-8">
                    <h5 class="card-title mb-3">
                        <i class="fas fa-briefcase me-2 text-primary"></i>
                        <?= esc($position['designation']) ?>
                    </h5>
                    <div class="row">
                        <div class="col-sm-6">
                            <p class="mb-1"><strong>Exercise:</strong> <?= esc($position['exercise_name']) ?></p>
                            <p class="mb-1"><strong>Position Group:</strong> <?= esc($position['group_name']) ?></p>
                        </div>
                        <div class="col-sm-6">
                            <p class="mb-1"><strong>Classification:</strong> <?= esc($position['classification']) ?></p>
                            <p class="mb-1"><strong>Location:</strong> <?= esc($position['location']) ?></p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 text-end">
                    <a href="<?= base_url('shortlisting/positions/' . $position['position_group_id']) ?>" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-1"></i> Back to Positions
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Flash Messages -->
    <?php if (session()->getFlashdata('success')): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>
            <?= session()->getFlashdata('success') ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if (session()->getFlashdata('error')): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i>
            <?= session()->getFlashdata('error') ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- Applications List -->
    <div class="card shadow-sm">
        <div class="card-header bg-white py-3">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-users me-2"></i>
                    Applications (<?= count($applications) ?>)
                </h5>
                <small class="text-muted">Sorted by highest score to lowest</small>
            </div>
        </div>
        <div class="card-body p-0">
            <?php if (empty($applications)): ?>
                <div class="text-center py-5">
                    <i class="fas fa-file-alt fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No rated applications found</h5>
                    <p class="text-muted">No applications are available for shortlisting. Applications must be rated first.</p>
                </div>
            <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-hover align-middle mb-0">
                        <thead class="table-light">
                            <tr>
                                <th class="px-4">Rank</th>
                                <th>Applicant Name</th>
                                <th>Application No.</th>
                                <th>Rating Score</th>
                                <th>Shortlist Status</th>
                                <th>Action</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($applications as $index => $application): ?>
                                <tr>
                                    <td class="px-4">
                                        <span class="badge bg-primary">#<?= $index + 1 ?></span>
                                    </td>
                                    <td>
                                        <div>
                                            <strong><?= esc($application['first_name']) ?> <?= esc($application['last_name']) ?></strong>
                                            <br><small class="text-muted"><?= esc($application['gender']) ?></small>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-info">
                                            <?= esc($application['application_number']) ?>
                                        </span>
                                    </td>
                                    <td>
                                        <div class="text-center">
                                            <strong class="text-primary"><?= $application['rating_capability_max'] ?? 0 ?></strong>
                                            <br><small class="text-muted">Total Score</small>
                                        </div>
                                    </td>
                                    <td>
                                        <?php
                                        $status = $application['shortlist_status'] ?? 'pending';
                                        $badgeClass = match($status) {
                                            'shortlisted' => 'bg-success',
                                            'eliminated' => 'bg-danger',
                                            'withdrawn' => 'bg-warning',
                                            default => 'bg-secondary'
                                        };
                                        ?>
                                        <span class="badge <?= $badgeClass ?>">
                                            <?= ucfirst($status) ?>
                                        </span>
                                    </td>
                                    <td>
                                        <a href="<?= base_url('shortlisting/detail/' . $application['id']) ?>" 
                                           class="btn btn-primary btn-sm">
                                            <i class="fas fa-edit"></i> Shortlist
                                        </a>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>
<?= $this->endSection() ?>
