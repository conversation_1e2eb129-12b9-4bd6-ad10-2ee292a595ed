<?php
/**
 * Interviews List - Manage Interviews for Exercise
 * Shows interviews for a specific exercise with CRUD operations
 */
?>

<?= $this->extend('templates/nolstemp') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h4 class="mb-1">Manage Interviews</h4>
                    <p class="text-muted mb-0">Exercise: <?= esc($exercise['exercise_name']) ?></p>
                </div>
                <div>
                    <a href="<?= base_url('interviews') ?>" class="btn btn-outline-secondary me-2">
                        <i class="fas fa-arrow-left me-2"></i>Back to Exercises
                    </a>
                    <a href="<?= base_url('interviews/create/' . $exercise['id']) ?>" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>Create Interview
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Success/Error Messages -->
    <?php if (session()->getFlashdata('success')): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <?= session()->getFlashdata('success') ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if (session()->getFlashdata('error')): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <?= session()->getFlashdata('error') ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- Interviews Card -->
    <div class="card shadow-sm">
        <div class="card-header bg-white py-3">
            <div class="row align-items-center">
                <div class="col">
                    <h5 class="mb-0">
                        <i class="fas fa-users text-primary me-2"></i>
                        Interviews
                    </h5>
                </div>
                <div class="col-auto">
                    <span class="badge bg-info text-dark">
                        <?= count($interviews) ?> Interview(s)
                    </span>
                </div>
            </div>
        </div>
        <div class="card-body">
            <?php if (empty($interviews)): ?>
                <div class="text-center py-5">
                    <div class="mb-3">
                        <i class="fas fa-users text-muted" style="font-size: 3rem;"></i>
                    </div>
                    <h5 class="text-muted">No Interviews Created</h5>
                    <p class="text-muted">Create your first interview to start managing the interview process for this exercise.</p>
                    <a href="<?= base_url('interviews/create/' . $exercise['id']) ?>" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>Create Interview
                    </a>
                </div>
            <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-light">
                            <tr>
                                <th>Interview Title</th>
                                <th>Created</th>
                                <th>Remarks</th>
                                <th class="text-center">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($interviews as $interview): ?>
                                <tr>
                                    <td>
                                        <div>
                                            <h6 class="mb-1"><?= esc($interview['interview_title']) ?></h6>
                                            <small class="text-muted">
                                                <i class="fas fa-calendar me-1"></i>
                                                ID: <?= $interview['id'] ?>
                                            </small>
                                        </div>
                                    </td>
                                    <td>
                                        <div>
                                            <div class="fw-medium">
                                                <?= date('M d, Y', strtotime($interview['created_at'])) ?>
                                            </div>
                                            <small class="text-muted">
                                                <?= date('h:i A', strtotime($interview['created_at'])) ?>
                                            </small>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="text-truncate" style="max-width: 200px;">
                                            <?= esc($interview['remarks'] ?: 'No remarks') ?>
                                        </div>
                                    </td>
                                    <td class="text-center">
                                        <div class="btn-group" role="group">
                                            <a href="<?= base_url('interviews/dashboard/' . $interview['id']) ?>" 
                                               class="btn btn-primary btn-sm" 
                                               title="Interview Dashboard">
                                                <i class="fas fa-tachometer-alt me-1"></i>
                                                Dashboard
                                            </a>
                                            <button type="button" class="btn btn-outline-secondary btn-sm dropdown-toggle dropdown-toggle-split" data-bs-toggle="dropdown">
                                                <span class="visually-hidden">Toggle Dropdown</span>
                                            </button>
                                            <ul class="dropdown-menu">
                                                <li>
                                                    <a class="dropdown-item" href="<?= base_url('interviews/edit/' . $interview['id']) ?>">
                                                        <i class="fas fa-edit me-2"></i>Edit
                                                    </a>
                                                </li>
                                                <li><hr class="dropdown-divider"></li>
                                                <li>
                                                    <a class="dropdown-item text-danger" href="#" 
                                                       onclick="confirmDelete(<?= $interview['id'] ?>, '<?= esc($interview['interview_title']) ?>')">
                                                        <i class="fas fa-trash me-2"></i>Delete
                                                    </a>
                                                </li>
                                            </ul>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title" id="deleteModalLabel">
                    <i class="fas fa-exclamation-triangle me-2"></i>Confirm Delete
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete the interview "<strong id="deleteInterviewTitle"></strong>"?</p>
                <p class="text-muted mb-0">This action cannot be undone.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-trash me-2"></i>Delete Interview
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function confirmDelete(interviewId, interviewTitle) {
    document.getElementById('deleteInterviewTitle').textContent = interviewTitle;
    document.getElementById('deleteForm').action = `<?= base_url('interviews/delete/') ?>${interviewId}`;
    
    const deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
    deleteModal.show();
}
</script>
<?= $this->endSection() ?>
