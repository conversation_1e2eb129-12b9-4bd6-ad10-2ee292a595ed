<?php

namespace App\Models;

use CodeIgniter\Model;

/**
 * InterviewPositionsModel
 * 
 * Model for managing interview positions table
 * 
 * Table Structure:
 * CREATE TABLE interview_positions (
 *     id INT AUTO_INCREMENT PRIMARY KEY,
 *     org_id INT NOT NULL,
 *     exercise_id INT NOT NULL,
 *     position_id INT NOT NULL,
 *     priority INT NULL, -- can be left empty
 *     created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
 *     updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
 * );
 * 
 * Indexes:
 * CREATE INDEX idx_interview_positions_org_id ON interview_positions(org_id);
 * CREATE INDEX idx_interview_positions_exercise_id ON interview_positions(exercise_id);
 * CREATE INDEX idx_interview_positions_position_id ON interview_positions(position_id);
 */
class InterviewPositionsModel extends Model
{
    protected $table         = 'interview_positions';
    protected $primaryKey    = 'id';
    protected $useAutoIncrement = true;
    protected $returnType    = 'array';
    protected $useSoftDeletes = false;
    protected $protectFields = true;

    // Fields that are allowed to be set during insert/update operations.
    protected $allowedFields = [
        'org_id',
        'exercise_id',
        'position_id',
        'priority'
    ];

    // Enable automatic handling of created_at and updated_at fields.
    protected $useTimestamps = true;
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $dateFormat    = 'datetime';

    // Validation rules
    protected $validationRules = [
        'org_id'      => 'required|numeric',
        'exercise_id' => 'required|numeric',
        'position_id' => 'required|numeric',
        'priority'    => 'permit_empty|numeric'
    ];

    protected $validationMessages = [
        'org_id' => [
            'required' => 'Organization ID is required',
            'numeric'  => 'Organization ID must be a number'
        ],
        'exercise_id' => [
            'required' => 'Exercise ID is required',
            'numeric'  => 'Exercise ID must be a number'
        ],
        'position_id' => [
            'required' => 'Position ID is required',
            'numeric'  => 'Position ID must be a number'
        ],
        'priority' => [
            'numeric' => 'Priority must be a number'
        ]
    ];

    protected $skipValidation = false;
    protected $cleanValidationRules = true;

    /**
     * Get interview positions by organization ID
     *
     * @param int $orgId
     * @return array
     */
    public function getInterviewPositionsByOrgId($orgId)
    {
        return $this->where('org_id', $orgId)
                    ->orderBy('priority', 'ASC')
                    ->orderBy('created_at', 'DESC')
                    ->findAll();
    }

    /**
     * Get interview positions by exercise ID
     *
     * @param int $exerciseId
     * @return array
     */
    public function getInterviewPositionsByExerciseId($exerciseId)
    {
        return $this->where('exercise_id', $exerciseId)
                    ->orderBy('priority', 'ASC')
                    ->orderBy('created_at', 'DESC')
                    ->findAll();
    }

    /**
     * Get interview positions by position ID
     *
     * @param int $positionId
     * @return array
     */
    public function getInterviewPositionsByPositionId($positionId)
    {
        return $this->where('position_id', $positionId)
                    ->orderBy('priority', 'ASC')
                    ->orderBy('created_at', 'DESC')
                    ->findAll();
    }

    /**
     * Get interview positions with related data (organization, exercise, position details)
     *
     * @param int $exerciseId Optional exercise ID to filter by
     * @return array
     */
    public function getInterviewPositionsWithDetails($exerciseId = null)
    {
        $builder = $this->select('
            interview_positions.*,
            dakoii_org.org_name,
            dakoii_org.org_code,
            exercises.exercise_name,
            exercises.advertisement_no,
            positions.designation,
            positions.classification,
            positions.location,
            positions_groups.group_name
        ')
        ->join('dakoii_org', 'interview_positions.org_id = dakoii_org.id', 'left')
        ->join('exercises', 'interview_positions.exercise_id = exercises.id', 'left')
        ->join('positions', 'interview_positions.position_id = positions.id', 'left')
        ->join('positions_groups', 'positions.position_group_id = positions_groups.id', 'left');

        if ($exerciseId !== null) {
            $builder->where('interview_positions.exercise_id', $exerciseId);
        }

        return $builder->orderBy('interview_positions.priority', 'ASC')
                      ->orderBy('interview_positions.created_at', 'DESC')
                      ->findAll();
    }

    /**
     * Get interview positions ordered by priority
     *
     * @param int $exerciseId
     * @return array
     */
    public function getInterviewPositionsByPriority($exerciseId)
    {
        return $this->where('exercise_id', $exerciseId)
                    ->orderBy('priority', 'ASC')
                    ->orderBy('id', 'ASC')
                    ->findAll();
    }

    /**
     * Check if a position is already added for interview in an exercise
     *
     * @param int $exerciseId
     * @param int $positionId
     * @return bool
     */
    public function isPositionAlreadyAdded($exerciseId, $positionId)
    {
        $result = $this->where('exercise_id', $exerciseId)
                      ->where('position_id', $positionId)
                      ->first();
        
        return $result !== null;
    }

    /**
     * Update priority for multiple interview positions
     *
     * @param array $priorityUpdates Array of ['id' => priority] pairs
     * @return bool
     */
    public function updatePriorities($priorityUpdates)
    {
        $this->db->transStart();

        foreach ($priorityUpdates as $id => $priority) {
            $this->update($id, ['priority' => $priority]);
        }

        $this->db->transComplete();
        return $this->db->transStatus();
    }

    /**
     * Get interview positions count by exercise ID
     *
     * @param int $exerciseId
     * @return int
     */
    public function getInterviewPositionsCount($exerciseId)
    {
        return $this->where('exercise_id', $exerciseId)->countAllResults();
    }
}
