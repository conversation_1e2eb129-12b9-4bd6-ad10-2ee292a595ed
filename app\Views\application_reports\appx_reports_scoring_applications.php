<?php
/**
 * View file for Scoring Report - Applications List with Scores
 *
 * @var array $position Position details
 * @var array $applications List of applications with scoring data
 */
?>

<?= $this->extend('templates/nolstemp') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Breadcrumb Navigation -->
    <div class="row mb-3">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item">
                        <a href="<?= base_url('reports/exercises') ?>">Reports</a>
                    </li>
                    <li class="breadcrumb-item">
                        <a href="<?= base_url('reports/dashboard/' . $position['exercise_id']) ?>">Dashboard</a>
                    </li>
                    <li class="breadcrumb-item">
                        <a href="<?= base_url('reports/scoring/' . $position['exercise_id']) ?>">Scoring Report</a>
                    </li>
                    <li class="breadcrumb-item">
                        <a href="<?= base_url('reports/scoring/groups/' . $position['position_group_id']) ?>"><?= esc($position['group_name']) ?></a>
                    </li>
                    <li class="breadcrumb-item active" aria-current="page">
                        <?= esc($position['designation']) ?>
                    </li>
                </ol>
            </nav>
        </div>
    </div>

    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="h3 mb-1">Scoring Report - Applications</h2>
                    <p class="text-muted mb-0"><?= esc($position['designation']) ?></p>
                </div>
                <div>
                    <a href="<?= base_url('reports/scoring/groups/' . $position['position_group_id']) ?>" class="btn btn-outline-primary me-2">
                        <i class="fas fa-arrow-left me-1"></i>
                        Back to Positions
                    </a>
                    <button type="button" class="btn btn-danger" onclick="exportApplicationsScoringPDF()">
                        <i class="fas fa-file-pdf me-1"></i>
                        Export PDF
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Applications List -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-users me-2"></i>
                        Applications with Scoring
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (empty($applications)): ?>
                        <div class="text-center py-5">
                            <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No Applications Found</h5>
                            <p class="text-muted">There are no applications available for this position.</p>
                            <a href="<?= base_url('reports/scoring/groups/' . $position['position_group_id']) ?>" class="btn btn-primary">
                                <i class="fas fa-arrow-left"></i> Back to Positions
                            </a>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-bordered table-hover" id="applicationsTable">
                                <thead class="table-light">
                                    <tr>
                                        <th width="10%">Rank</th>
                                        <th width="25%">Application No.</th>
                                        <th width="45%">Applicant Details</th>
                                        <th width="15%">Total Score</th>
                                        <th width="15%">Percentage</th>
                                        <th width="15%">Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $rank = 1; ?>
                                    <?php foreach ($applications as $application): ?>
                                        <tr>
                                            <td>
                                                <?= $rank++ ?>
                                            </td>
                                            <td>
                                                <strong><?= esc($application['application_number']) ?></strong>
                                            </td>
                                            <td>
                                                <div>
                                                    <strong><?= esc($application['first_name'] . ' ' . $application['last_name']) ?></strong>
                                                    <br>
                                                    <small class="text-muted">
                                                        <i class="fas fa-envelope me-1"></i>
                                                        <?= esc($application['email_address']) ?>
                                                    </small>
                                                </div>
                                            </td>
                                            <td>
                                                <div class="text-center">
                                                    <strong class="text-primary">
                                                        <?= number_format($application['total_score'], 1) ?>
                                                    </strong>
                                                    <small class="text-muted d-block">
                                                        / <?= number_format($application['max_score'], 1) ?>
                                                    </small>
                                                </div>
                                            </td>
                                            <td>
                                                <div class="text-center">
                                                    <?= number_format($application['percentage'], 1) ?>%
                                                </div>
                                            </td>
                                            <td>
                                                <a href="<?= base_url('reports/scoring/applications/' . $application['id']) ?>" 
                                                   class="btn btn-primary btn-sm">
                                                    <i class="fas fa-eye me-1"></i>
                                                    View Details
                                                </a>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Rating Criteria and Scores -->
    <?php if (!empty($rating_matrix['matrix']) && !empty($applications)): ?>
    <div class="row mt-4">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header bg-success text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-bar me-2"></i>
                        Rating Criteria and Scores
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered table-hover" id="ratingMatrixTable">
                            <thead class="table-light">
                                <tr>
                                    <th width="25%">CRITERIA</th>
                                    <?php for ($i = 0; $i < count($applications); $i++): ?>
                                        <th width="<?= 75 / count($applications) ?>%" class="text-center">
                                            APPLICANT <?= $i + 1 ?>
                                        </th>
                                    <?php endfor; ?>
                                    <th width="10%" class="text-center">OUT OF</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($rating_matrix['matrix'] as $row): ?>
                                    <tr <?= $row['criterion'] === 'Total' ? 'class="table-dark fw-bold"' : '' ?>>
                                        <td><?= esc($row['criterion']) ?></td>
                                        <?php for ($i = 0; $i < count($applications); $i++): ?>
                                            <td class="text-center">
                                                <?= isset($row['scores'][$i]) ? number_format($row['scores'][$i], 0) : '0' ?>
                                            </td>
                                        <?php endfor; ?>
                                        <td class="text-center">
                                            <?php
                                            // Show the max score (should be same for all applications for this criterion)
                                            $maxScore = !empty($row['out_of']) ? max($row['out_of']) : 0;
                                            echo number_format($maxScore, 0);
                                            ?>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <!-- Position Information -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">Position Information</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <dl class="row">
                                <dt class="col-sm-4">Position:</dt>
                                <dd class="col-sm-8"><?= esc($position['designation']) ?></dd>
                                <dt class="col-sm-4">Reference:</dt>
                                <dd class="col-sm-8"><?= esc($position['position_reference']) ?></dd>
                                <dt class="col-sm-4">Classification:</dt>
                                <dd class="col-sm-8"><?= esc($position['classification']) ?></dd>
                            </dl>
                        </div>
                        <div class="col-md-6">
                            <dl class="row">
                                <dt class="col-sm-4">Award:</dt>
                                <dd class="col-sm-8"><?= esc($position['award']) ?></dd>
                                <dt class="col-sm-4">Location:</dt>
                                <dd class="col-sm-8"><?= esc($position['location']) ?></dd>
                                <dt class="col-sm-4">Total Applications:</dt>
                                <dd class="col-sm-8">
                                    <span class="badge bg-info"><?= count($applications) ?></span>
                                </dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Hidden form for PDF export -->
<form id="pdfExportForm" method="post" action="<?= base_url('reports/scoring/positions/export') ?>" style="display: none;">
    <?= csrf_field() ?>
    <input type="hidden" name="position_id" value="<?= $position['id'] ?>">
</form>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
$(document).ready(function() {
    // Initialize DataTable for Applications
    $('#applicationsTable').DataTable({
        responsive: true,
        pageLength: 25,
        order: [[4, 'desc']], // Sort by percentage descending
        columnDefs: [
            { orderable: false, targets: [5] } // Disable sorting for Actions column
        ]
    });

    // Initialize DataTable for Rating Matrix
    $('#ratingMatrixTable').DataTable({
        responsive: true,
        paging: false,
        searching: false,
        info: false,
        ordering: false, // Disable all sorting to preserve row order
        columnDefs: [
            { orderable: false, targets: '_all' } // Disable sorting for all columns
        ]
    });
});

function exportApplicationsScoringPDF() {
    // Show loading state
    const exportBtn = document.querySelector('button[onclick="exportApplicationsScoringPDF()"]');
    const originalText = exportBtn.innerHTML;
    exportBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Generating PDF...';
    exportBtn.disabled = true;

    try {
        // Submit the form to trigger PDF download
        document.getElementById('pdfExportForm').submit();

        // Reset button after a short delay
        setTimeout(() => {
            exportBtn.innerHTML = originalText;
            exportBtn.disabled = false;
        }, 2000);

    } catch (error) {
        console.error('PDF Export Error:', error);
        alert('Failed to generate PDF. Please try again.');

        // Reset button
        exportBtn.innerHTML = originalText;
        exportBtn.disabled = false;
    }
}
</script>
<?= $this->endSection() ?>
