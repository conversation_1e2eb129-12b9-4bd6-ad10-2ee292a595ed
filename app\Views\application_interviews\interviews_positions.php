<?php
/**
 * Manage Interview Positions
 * Page for managing positions in an interview
 */
?>

<?= $this->extend('templates/nolstemp') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h4 class="mb-1">Manage Positions</h4>
                    <p class="text-muted mb-0"><?= esc($interview['interview_title']) ?></p>
                </div>
                <div>
                    <a href="<?= base_url('interviews/dashboard/' . $interview['id']) ?>" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Positions Management -->
    <div class="card shadow-sm">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0">
                <i class="fas fa-users me-2"></i>Interview Positions
            </h5>
        </div>
        <div class="card-body">
            <?php if (empty($interview_positions)): ?>
                <div class="text-center py-5">
                    <div class="mb-3">
                        <i class="fas fa-users text-muted" style="font-size: 3rem;"></i>
                    </div>
                    <h5 class="text-muted">No Positions Added</h5>
                    <p class="text-muted">Add positions to this interview to start the interview process.</p>
                    <button class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>Add Positions
                    </button>
                </div>
            <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-light">
                            <tr>
                                <th>Position</th>
                                <th>Organization</th>
                                <th>Location</th>
                                <th>Priority</th>
                                <th class="text-center">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($interview_positions as $position): ?>
                                <tr>
                                    <td>
                                        <div>
                                            <h6 class="mb-1"><?= esc($position['designation']) ?></h6>
                                            <small class="text-muted"><?= esc($position['classification']) ?></small>
                                        </div>
                                    </td>
                                    <td><?= esc($position['org_name']) ?></td>
                                    <td><?= esc($position['location']) ?></td>
                                    <td>
                                        <span class="badge bg-secondary">
                                            <?= $position['priority'] ?: 'Not Set' ?>
                                        </span>
                                    </td>
                                    <td class="text-center">
                                        <button class="btn btn-outline-primary btn-sm">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn btn-outline-danger btn-sm">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>
<?= $this->endSection() ?>
