-- Core interview table (simple version)
CREATE TABLE interviews (
    id INT AUTO_INCREMENT PRIMARY KEY,
    org_id INT NOT NULL,
    exercise_id INT NOT NULL,

    interview_title VARCHAR(255) NOT NULL,
    interview_settings JSON NOT NULL,
    remarks TEXT,
    status VARCHAR,

    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Helpful indexes (optional)
CREATE INDEX idx_interviews_org_id ON interviews(org_id);
CREATE INDEX idx_interviews_exercise_id ON interviews(exercise_id);


CREATE TABLE interview_positions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    org_id INT NOT NULL,
    exercise_id INT NOT NULL,
    position_id INT NOT NULL,
    priority INT NOT NULL DEFAULT 1,

    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Helpful indexes (optional)
CREATE INDEX idx_interview_positions_org_id ON interview_positions(org_id);
CREATE INDEX idx_interview_positions_exercise_id ON interview_positions(exercise_id);
CREATE INDEX idx_interview_positions_position_id ON interview_positions(position_id);


