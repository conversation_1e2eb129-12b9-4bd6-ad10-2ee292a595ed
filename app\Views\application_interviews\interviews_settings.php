<?php
/**
 * Interview Settings
 * Page for configuring interview settings
 */
?>

<?= $this->extend('templates/nolstemp') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h4 class="mb-1">Interview Settings</h4>
                    <p class="text-muted mb-0"><?= esc($interview['interview_title']) ?></p>
                </div>
                <div>
                    <a href="<?= base_url('interviews/dashboard/' . $interview['id']) ?>" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Success/Error Messages -->
    <?php if (session()->getFlashdata('success')): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <?= session()->getFlashdata('success') ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if (session()->getFlashdata('error')): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <?= session()->getFlashdata('error') ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- Settings Form -->
    <div class="card shadow-sm">
        <div class="card-header bg-success text-white">
            <h5 class="mb-0">
                <i class="fas fa-cog me-2"></i>Interview Configuration
            </h5>
        </div>
        <div class="card-body">
            <form action="<?= base_url('interviews/settings/save/' . $interview['id']) ?>" method="POST">
                <?= csrf_field() ?>
                
                <div class="row g-3">
                    <!-- Interview Duration -->
                    <div class="col-md-4">
                        <label for="interview_duration" class="form-label">Interview Duration (minutes)</label>
                        <input type="number" 
                               class="form-control" 
                               id="interview_duration" 
                               name="settings[interview_duration]" 
                               value="<?= $settings['interview_duration'] ?? 60 ?>"
                               min="15" 
                               max="240">
                    </div>

                    <!-- Break Duration -->
                    <div class="col-md-4">
                        <label for="break_duration" class="form-label">Break Duration (minutes)</label>
                        <input type="number" 
                               class="form-control" 
                               id="break_duration" 
                               name="settings[break_duration]" 
                               value="<?= $settings['break_duration'] ?? 15 ?>"
                               min="0" 
                               max="60">
                    </div>

                    <!-- Interview Type -->
                    <div class="col-md-4">
                        <label for="interview_type" class="form-label">Interview Type</label>
                        <select class="form-select" id="interview_type" name="settings[interview_type]">
                            <option value="panel" <?= ($settings['interview_type'] ?? 'panel') === 'panel' ? 'selected' : '' ?>>Panel Interview</option>
                            <option value="individual" <?= ($settings['interview_type'] ?? '') === 'individual' ? 'selected' : '' ?>>Individual Interview</option>
                            <option value="group" <?= ($settings['interview_type'] ?? '') === 'group' ? 'selected' : '' ?>>Group Interview</option>
                        </select>
                    </div>

                    <!-- Start Time -->
                    <div class="col-md-6">
                        <label for="start_time" class="form-label">Start Time</label>
                        <input type="time" 
                               class="form-control" 
                               id="start_time" 
                               name="settings[start_time]" 
                               value="<?= $settings['start_time'] ?? '09:00' ?>">
                    </div>

                    <!-- End Time -->
                    <div class="col-md-6">
                        <label for="end_time" class="form-label">End Time</label>
                        <input type="time" 
                               class="form-control" 
                               id="end_time" 
                               name="settings[end_time]" 
                               value="<?= $settings['end_time'] ?? '17:00' ?>">
                    </div>

                    <!-- Venue -->
                    <div class="col-12">
                        <label for="venue" class="form-label">Venue</label>
                        <input type="text" 
                               class="form-control" 
                               id="venue" 
                               name="settings[venue]" 
                               value="<?= $settings['venue'] ?? '' ?>"
                               placeholder="Enter interview venue">
                    </div>

                    <!-- Special Instructions -->
                    <div class="col-12">
                        <label for="special_instructions" class="form-label">Special Instructions</label>
                        <textarea class="form-control" 
                                  id="special_instructions" 
                                  name="settings[special_instructions]" 
                                  rows="3"
                                  placeholder="Enter any special instructions for candidates"><?= $settings['special_instructions'] ?? '' ?></textarea>
                    </div>

                    <!-- Notification Settings -->
                    <div class="col-12">
                        <h6 class="border-bottom pb-2 mb-3">Notification Settings</h6>
                    </div>

                    <div class="col-md-6">
                        <div class="form-check">
                            <input class="form-check-input" 
                                   type="checkbox" 
                                   id="notification_enabled" 
                                   name="settings[notification_enabled]" 
                                   value="1"
                                   <?= ($settings['notification_enabled'] ?? true) ? 'checked' : '' ?>>
                            <label class="form-check-label" for="notification_enabled">
                                Enable Email Notifications
                            </label>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <label for="reminder_days" class="form-label">Reminder Days Before Interview</label>
                        <input type="number" 
                               class="form-control" 
                               id="reminder_days" 
                               name="settings[reminder_days]" 
                               value="<?= $settings['reminder_days'] ?? 3 ?>"
                               min="0" 
                               max="30">
                    </div>
                </div>

                <!-- Form Actions -->
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="d-flex justify-content-end gap-2">
                            <a href="<?= base_url('interviews/dashboard/' . $interview['id']) ?>" class="btn btn-secondary">
                                <i class="fas fa-times me-2"></i>Cancel
                            </a>
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-save me-2"></i>Save Settings
                            </button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
<?= $this->endSection() ?>
