<?= $this->extend('templates/nolstemp') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Page Header -->
    <div class="card mb-4 border-start border-primary border-4">
        <div class="card-body">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="h4 fw-bold text-gray-800 mb-1">Shortlisting - Position Groups</h2>
                    <p class="text-muted mb-0">Select a position group to continue</p>
                </div>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb mb-0">
                        <li class="breadcrumb-item"><a href="<?= base_url('shortlisting') ?>" class="text-decoration-none">Exercises</a></li>
                        <li class="breadcrumb-item active" aria-current="page"><?= esc($exercise['exercise_name']) ?></li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>

    <!-- Exercise Information -->
    <div class="card mb-4">
        <div class="card-body">
            <div class="row">
                <div class="col-md-8">
                    <h5 class="card-title mb-3">
                        <i class="fas fa-clipboard-list me-2 text-primary"></i>
                        <?= esc($exercise['exercise_name']) ?>
                    </h5>
                    <div class="row">
                        <div class="col-sm-6">
                            <p class="mb-1"><strong>Advertisement No:</strong> <?= esc($exercise['advertisement_no']) ?></p>
                        </div>
                        <div class="col-sm-6">
                            <p class="mb-1"><strong>Organization:</strong> <?= esc($exercise['org_name']) ?></p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 text-end">
                    <a href="<?= base_url('shortlisting') ?>" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-1"></i> Back to Exercises
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Flash Messages -->
    <?php if (session()->getFlashdata('success')): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>
            <?= session()->getFlashdata('success') ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if (session()->getFlashdata('error')): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i>
            <?= session()->getFlashdata('error') ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- Position Groups List -->
    <div class="card shadow-sm">
        <div class="card-header bg-white py-3">
            <h5 class="card-title mb-0">Position Groups</h5>
        </div>
        <div class="card-body p-0">
            <?php if (empty($positionGroups)): ?>
                <div class="text-center py-5">
                    <i class="fas fa-users fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No position groups found</h5>
                    <p class="text-muted">No position groups are available for shortlisting in this exercise.</p>
                </div>
            <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-hover align-middle mb-0">
                        <thead class="table-light">
                            <tr>
                                <th class="px-4">Group Name</th>
                                <th>Number of Positions</th>
                                <th>Action</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($positionGroups as $group): ?>
                                <tr>
                                    <td class="px-4">
                                        <strong><?= esc($group['group_name']) ?></strong>
                                    </td>
                                    <td>
                                        <span class="badge bg-info">
                                            <?= $group['position_count'] ?> Position<?= $group['position_count'] != 1 ? 's' : '' ?>
                                        </span>
                                    </td>
                                    <td>
                                        <a href="<?= base_url('shortlisting/positions/' . $group['id']) ?>" 
                                           class="btn btn-primary btn-sm">
                                            <i class="fas fa-arrow-right"></i> View Positions
                                        </a>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>
<?= $this->endSection() ?>
